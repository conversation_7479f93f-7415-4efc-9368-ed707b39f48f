{"Version": 3, "Meta": {"Duration": 10.4, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 37, "TotalSegmentCount": 405, "TotalPointCount": 1216, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 0, 10.4, 1]}, {"Target": "Model", "Id": "EyeBlink", "Segments": [0, 0.997, 1, 1.511, 0.997, 3.022, 1, 4.533, 1, 1, 4.567, 1, 4.6, 0, 4.633, 0, 1, 4.667, 0, 4.7, 0, 4.733, 0, 1, 4.789, 0, 4.844, 1, 4.9, 1, 1, 5.5, 1, 6.1, 1, 6.7, 1, 1, 6.733, 1, 6.767, 0, 6.8, 0, 1, 6.833, 0, 6.867, 0, 6.9, 0, 1, 6.956, 0, 7.011, 1, 7.067, 1, 0, 10.4, 1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, -19, 1, 0.211, -19, 0.422, 19, 0.633, 19, 1, 0.856, 19, 1.078, -19, 1.3, -19, 1, 1.511, -19, 1.722, 19, 1.933, 19, 1, 2.156, 19, 2.378, -19, 2.6, -19, 1, 2.822, -19, 3.044, 19, 3.267, 19, 1, 3.478, 19, 3.689, -19, 3.9, -19, 1, 4.111, -19, 4.322, 8.753, 4.533, 19, 1, 4.756, 29.786, 4.978, 30, 5.2, 30, 1, 5.422, 30, 5.644, 30, 5.867, 30, 1, 6.078, 30, 6.289, 30, 6.5, 30, 1, 6.711, 30, 6.922, 29.887, 7.133, 19, 1, 7.356, 7.541, 7.578, -19, 7.8, -19, 1, 8.022, -19, 8.244, 19, 8.467, 19, 1, 8.689, 19, 8.911, -19, 9.133, -19, 1, 9.333, -19, 9.533, 19, 9.733, 19, 1, 9.956, 19, 10.178, -19, 10.4, -19]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -18.45, 1, 0.089, -18.45, 0.178, -6, 0.267, -6, 1, 0.389, -6, 0.511, -18.5, 0.633, -18.5, 1, 0.733, -18.5, 0.833, -6, 0.933, -6, 1, 1.056, -6, 1.178, -18.45, 1.3, -18.45, 1, 1.4, -18.45, 1.5, -6, 1.6, -6, 1, 1.711, -6, 1.822, -18.5, 1.933, -18.5, 1, 2.044, -18.5, 2.156, -6, 2.267, -6, 1, 2.378, -6, 2.489, -18.45, 2.6, -18.45, 1, 2.7, -18.45, 2.8, -6, 2.9, -6, 1, 3.022, -6, 3.144, -18.5, 3.267, -18.5, 1, 3.356, -18.5, 3.444, -6, 3.533, -6, 1, 3.656, -6, 3.778, -18.45, 3.9, -18.45, 1, 4, -18.45, 4.1, -6, 4.2, -6, 1, 4.311, -6, 4.422, -18.5, 4.533, -18.5, 1, 4.644, -18.5, 4.756, -6, 4.867, -6, 1, 4.978, -6, 5.089, -18.45, 5.2, -18.45, 1, 5.3, -18.45, 5.4, -6, 5.5, -6, 1, 5.622, -6, 5.744, -18.5, 5.867, -18.5, 1, 5.956, -18.5, 6.044, -6, 6.133, -6, 1, 6.256, -6, 6.378, -18.45, 6.5, -18.45, 1, 6.6, -18.45, 6.7, -6, 6.8, -6, 1, 6.911, -6, 7.022, -18.5, 7.133, -18.5, 1, 7.244, -18.5, 7.356, -6, 7.467, -6, 1, 7.578, -6, 7.689, -18.45, 7.8, -18.45, 1, 7.9, -18.45, 8, -6, 8.1, -6, 1, 8.222, -6, 8.344, -18.5, 8.467, -18.5, 1, 8.567, -18.5, 8.667, -6, 8.767, -6, 1, 8.889, -6, 9.011, -18.45, 9.133, -18.45, 1, 9.222, -18.45, 9.311, -6, 9.4, -6, 1, 9.511, -6, 9.622, -18.5, 9.733, -18.5, 1, 9.844, -18.5, 9.956, -6, 10.067, -6, 1, 10.178, -6, 10.289, -18.45, 10.4, -18.45]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -10.291, 1, 0.033, -10.291, 0.067, -13, 0.1, -13, 1, 0.322, -13, 0.544, 13, 0.767, 13, 1, 0.989, 13, 1.211, -13, 1.433, -13, 1, 1.644, -13, 1.856, 13, 2.067, 13, 1, 2.278, 13, 2.489, -13, 2.7, -13, 1, 2.922, -13, 3.144, 13, 3.367, 13, 1, 3.589, 13, 3.811, -13, 4.033, -13, 1, 4.244, -13, 4.456, 13, 4.667, 13, 1, 4.878, 13, 5.089, -13, 5.3, -13, 1, 5.522, -13, 5.744, 13, 5.967, 13, 1, 6.189, 13, 6.411, -13, 6.633, -13, 1, 6.856, -13, 7.078, 13, 7.3, 13, 1, 7.5, 13, 7.7, -13, 7.9, -13, 1, 8.122, -13, 8.344, 13, 8.567, 13, 1, 8.789, 13, 9.011, -13, 9.233, -13, 1, 9.456, -13, 9.678, 13, 9.9, 13, 1, 10.067, 13, 10.233, -10.291, 10.4, -10.291]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0.1, 1, 0.211, 0.1, 0.422, -0.1, 0.633, -0.1, 1, 0.856, -0.1, 1.078, 0.1, 1.3, 0.1, 1, 1.511, 0.1, 1.722, -0.1, 1.933, -0.1, 1, 2.156, -0.1, 2.378, 0.1, 2.6, 0.1, 1, 2.822, 0.1, 3.044, -0.1, 3.267, -0.1, 1, 3.478, -0.1, 3.689, 0.1, 3.9, 0.1, 1, 4.111, 0.1, 4.322, -0.1, 4.533, -0.1, 1, 4.567, -0.1, 4.6, 0, 4.633, 0, 1, 4.667, 0, 4.7, 0, 4.733, 0, 1, 4.789, 0, 4.844, 1, 4.9, 1, 1, 5.222, 1, 5.544, 1, 5.867, 1, 1, 6.133, 1, 6.4, 1, 6.667, 1, 1, 6.7, 1, 6.733, 0.794, 6.767, 0.5, 1, 6.789, 0.304, 6.811, 0.028, 6.833, 0, 1, 6.9, -0.085, 6.967, -0.1, 7.033, -0.1, 1, 7.067, -0.1, 7.1, -0.1, 7.133, -0.1, 1, 7.356, -0.1, 7.578, 0.1, 7.8, 0.1, 1, 8.022, 0.1, 8.244, -0.1, 8.467, -0.1, 1, 8.689, -0.1, 8.911, 0.1, 9.133, 0.1, 1, 9.333, 0.1, 9.533, -0.1, 9.733, -0.1, 1, 9.956, -0.1, 10.178, 0.1, 10.4, 0.1]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0.1, 1, 0.211, 0.1, 0.422, 0.1, 0.633, 0.1, 1, 0.856, 0.1, 1.078, 0.1, 1.3, 0.1, 1, 1.511, 0.1, 1.722, 0.1, 1.933, 0.1, 1, 2.156, 0.1, 2.378, 0.1, 2.6, 0.1, 1, 2.822, 0.1, 3.044, 0.1, 3.267, 0.1, 1, 3.478, 0.1, 3.689, 0.1, 3.9, 0.1, 1, 4.111, 0.1, 4.322, 0.1, 4.533, 0.1, 1, 4.567, 0.1, 4.6, -1, 4.633, -1, 1, 4.667, -1, 4.7, -1, 4.733, -1, 1, 4.789, -1, 4.844, 1, 4.9, 1, 1, 5.222, 1, 5.544, 1, 5.867, 1, 1, 6.133, 1, 6.4, 1, 6.667, 1, 1, 6.7, 1, 6.733, 0.36, 6.767, -0.4, 1, 6.789, -0.906, 6.811, -1, 6.833, -1, 1, 6.9, -1, 6.967, 0.1, 7.033, 0.1, 1, 7.067, 0.1, 7.1, 0.1, 7.133, 0.1, 1, 7.356, 0.1, 7.578, 0.1, 7.8, 0.1, 1, 8.022, 0.1, 8.244, 0.1, 8.467, 0.1, 1, 8.689, 0.1, 8.911, 0.1, 9.133, 0.1, 1, 9.333, 0.1, 9.533, 0.1, 9.733, 0.1, 1, 9.956, 0.1, 10.178, 0.1, 10.4, 0.1]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 1, 1, 0.211, 1, 0.422, 1, 0.633, 1, 1, 0.856, 1, 1.078, 1, 1.3, 1, 1, 1.511, 1, 1.722, 1, 1.933, 1, 1, 2.156, 1, 2.378, 1, 2.6, 1, 1, 2.822, 1, 3.044, 1, 3.267, 1, 1, 3.478, 1, 3.689, 1, 3.9, 1, 1, 4.111, 1, 4.322, 1, 4.533, 1, 1, 4.756, 1, 4.978, 1, 5.2, 1, 1, 5.422, 1, 5.644, 1, 5.867, 1, 1, 6.078, 1, 6.289, 1, 6.5, 1, 1, 6.711, 1, 6.922, 1, 7.133, 1, 1, 7.356, 1, 7.578, 1, 7.8, 1, 1, 8.022, 1, 8.244, 1, 8.467, 1, 1, 8.689, 1, 8.911, 1, 9.133, 1, 1, 9.333, 1, 9.533, 1, 9.733, 1, 1, 9.956, 1, 10.178, 1, 10.4, 1]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 1, 1, 0.211, 1, 0.422, 1, 0.633, 1, 1, 0.856, 1, 1.078, 1, 1.3, 1, 1, 1.511, 1, 1.722, 1, 1.933, 1, 1, 2.156, 1, 2.378, 1, 2.6, 1, 1, 2.822, 1, 3.044, 1, 3.267, 1, 1, 3.478, 1, 3.689, 1, 3.9, 1, 1, 4.111, 1, 4.322, 1, 4.533, 1, 1, 4.756, 1, 4.978, 1, 5.2, 1, 1, 5.422, 1, 5.644, 1, 5.867, 1, 1, 6.078, 1, 6.289, 1, 6.5, 1, 1, 6.711, 1, 6.922, 1, 7.133, 1, 1, 7.356, 1, 7.578, 1, 7.8, 1, 1, 8.022, 1, 8.244, 1, 8.467, 1, 1, 8.689, 1, 8.911, 1, 9.133, 1, 1, 9.333, 1, 9.533, 1, 9.733, 1, 1, 9.956, 1, 10.178, 1, 10.4, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.211, 0, 0.422, 0, 0.633, 0, 1, 0.856, 0, 1.078, 0, 1.3, 0, 1, 1.511, 0, 1.722, 0, 1.933, 0, 1, 2.156, 0, 2.378, 0, 2.6, 0, 1, 2.822, 0, 3.044, 0, 3.267, 0, 1, 3.478, 0, 3.689, 0, 3.9, 0, 1, 4.111, 0, 4.322, 0, 4.533, 0, 1, 4.756, 0, 4.978, 0, 5.2, 0, 1, 5.422, 0, 5.644, 0, 5.867, 0, 1, 6.078, 0, 6.289, 0, 6.5, 0, 1, 6.711, 0, 6.922, 0, 7.133, 0, 1, 7.356, 0, 7.578, 0, 7.8, 0, 1, 8.022, 0, 8.244, 0, 8.467, 0, 1, 8.689, 0, 8.911, 0, 9.133, 0, 1, 9.333, 0, 9.533, 0, 9.733, 0, 1, 9.956, 0, 10.178, 0, 10.4, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, -10, 1, 0.089, -10, 0.178, -8, 0.267, -8, 1, 0.389, -8, 0.511, -10, 0.633, -10, 1, 0.733, -10, 0.833, -8, 0.933, -8, 1, 1.056, -8, 1.178, -10, 1.3, -10, 1, 1.4, -10, 1.5, -8, 1.6, -8, 1, 1.711, -8, 1.822, -10, 1.933, -10, 1, 2.044, -10, 2.156, -8, 2.267, -8, 1, 2.378, -8, 2.489, -10, 2.6, -10, 1, 2.7, -10, 2.8, -8, 2.9, -8, 1, 3.022, -8, 3.144, -10, 3.267, -10, 1, 3.356, -10, 3.444, -8, 3.533, -8, 1, 3.656, -8, 3.778, -10, 3.9, -10, 1, 4, -10, 4.1, -8, 4.2, -8, 1, 4.311, -8, 4.422, -10, 4.533, -10, 1, 4.644, -10, 4.756, -8, 4.867, -8, 1, 4.978, -8, 5.089, -10, 5.2, -10, 1, 5.3, -10, 5.4, -8, 5.5, -8, 1, 5.622, -8, 5.744, -10, 5.867, -10, 1, 5.956, -10, 6.044, -8, 6.133, -8, 1, 6.256, -8, 6.378, -10, 6.5, -10, 1, 6.6, -10, 6.7, -8, 6.8, -8, 1, 6.911, -8, 7.022, -10, 7.133, -10, 1, 7.244, -10, 7.356, -8, 7.467, -8, 1, 7.578, -8, 7.689, -10, 7.8, -10, 1, 7.9, -10, 8, -8, 8.1, -8, 1, 8.222, -8, 8.344, -10, 8.467, -10, 1, 8.567, -10, 8.667, -8, 8.767, -8, 1, 8.889, -8, 9.011, -10, 9.133, -10, 1, 9.222, -10, 9.311, -8, 9.4, -8, 1, 9.511, -8, 9.622, -10, 9.733, -10, 1, 9.844, -10, 9.956, -8, 10.067, -8, 1, 10.178, -8, 10.289, -10, 10.4, -10]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, -10, 1, 0.089, -10, 0.178, -8, 0.267, -8, 1, 0.389, -8, 0.511, -10, 0.633, -10, 1, 0.733, -10, 0.833, -8, 0.933, -8, 1, 1.056, -8, 1.178, -10, 1.3, -10, 1, 1.4, -10, 1.5, -8, 1.6, -8, 1, 1.711, -8, 1.822, -10, 1.933, -10, 1, 2.044, -10, 2.156, -8, 2.267, -8, 1, 2.378, -8, 2.489, -10, 2.6, -10, 1, 2.7, -10, 2.8, -8, 2.9, -8, 1, 3.022, -8, 3.144, -10, 3.267, -10, 1, 3.356, -10, 3.444, -8, 3.533, -8, 1, 3.656, -8, 3.778, -10, 3.9, -10, 1, 4, -10, 4.1, -8, 4.2, -8, 1, 4.311, -8, 4.422, -10, 4.533, -10, 1, 4.644, -10, 4.756, -8, 4.867, -8, 1, 4.978, -8, 5.089, -10, 5.2, -10, 1, 5.3, -10, 5.4, -8, 5.5, -8, 1, 5.622, -8, 5.744, -10, 5.867, -10, 1, 5.956, -10, 6.044, -8, 6.133, -8, 1, 6.256, -8, 6.378, -10, 6.5, -10, 1, 6.6, -10, 6.7, -8, 6.8, -8, 1, 6.911, -8, 7.022, -10, 7.133, -10, 1, 7.244, -10, 7.356, -8, 7.467, -8, 1, 7.578, -8, 7.689, -10, 7.8, -10, 1, 7.9, -10, 8, -8, 8.1, -8, 1, 8.222, -8, 8.344, -10, 8.467, -10, 1, 8.567, -10, 8.667, -8, 8.767, -8, 1, 8.889, -8, 9.011, -10, 9.133, -10, 1, 9.222, -10, 9.311, -8, 9.4, -8, 1, 9.511, -8, 9.622, -10, 9.733, -10, 1, 9.844, -10, 9.956, -8, 10.067, -8, 1, 10.178, -8, 10.289, -10, 10.4, -10]}, {"Target": "Parameter", "Id": "ParamLeftLeg", "Segments": [0, 0, 1, 0.211, 0, 0.422, 0, 0.633, 0, 1, 0.856, 0, 1.078, 0, 1.3, 0, 1, 1.511, 0, 1.722, 0, 1.933, 0, 1, 2.156, 0, 2.378, 0, 2.6, 0, 1, 2.822, 0, 3.044, 0, 3.267, 0, 1, 3.478, 0, 3.689, 0, 3.9, 0, 1, 4.111, 0, 4.322, 0, 4.533, 0, 1, 4.756, 0, 4.978, 0, 5.2, 0, 1, 5.422, 0, 5.644, 0, 5.867, 0, 1, 6.078, 0, 6.289, 0, 6.5, 0, 1, 6.711, 0, 6.922, 0, 7.133, 0, 1, 7.356, 0, 7.578, 0, 7.8, 0, 1, 8.022, 0, 8.244, 0, 8.467, 0, 1, 8.689, 0, 8.911, 0, 9.133, 0, 1, 9.333, 0, 9.533, 0, 9.733, 0, 1, 9.956, 0, 10.178, 0, 10.4, 0]}, {"Target": "Parameter", "Id": "ParamRightLeg", "Segments": [0, 0, 1, 0.211, 0, 0.422, 0, 0.633, 0, 1, 0.856, 0, 1.078, 0, 1.3, 0, 1, 1.511, 0, 1.722, 0, 1.933, 0, 1, 2.156, 0, 2.378, 0, 2.6, 0, 1, 2.822, 0, 3.044, 0, 3.267, 0, 1, 3.478, 0, 3.689, 0, 3.9, 0, 1, 4.111, 0, 4.322, 0, 4.533, 0, 1, 4.756, 0, 4.978, 0, 5.2, 0, 1, 5.422, 0, 5.644, 0, 5.867, 0, 1, 6.078, 0, 6.289, 0, 6.5, 0, 1, 6.711, 0, 6.922, 0, 7.133, 0, 1, 7.356, 0, 7.578, 0, 7.8, 0, 1, 8.022, 0, 8.244, 0, 8.467, 0, 1, 8.689, 0, 8.911, 0, 9.133, 0, 1, 9.333, 0, 9.533, 0, 9.733, 0, 1, 9.956, 0, 10.178, 0, 10.4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, -1, 1, 0.211, -1, 0.422, 1, 0.633, 1, 1, 0.856, 1, 1.078, -1, 1.3, -1, 1, 1.511, -1, 1.722, 1, 1.933, 1, 1, 2.156, 1, 2.378, -1, 2.6, -1, 1, 2.822, -1, 3.044, 1, 3.267, 1, 1, 3.478, 1, 3.689, -1, 3.9, -1, 1, 4.111, -1, 4.322, 1, 4.533, 1, 1, 4.756, 1, 4.978, -1, 5.2, -1, 1, 5.422, -1, 5.644, 1, 5.867, 1, 1, 6.078, 1, 6.289, -1, 6.5, -1, 1, 6.711, -1, 6.922, 1, 7.133, 1, 1, 7.356, 1, 7.578, -1, 7.8, -1, 1, 8.022, -1, 8.244, 1, 8.467, 1, 1, 8.689, 1, 8.911, -1, 9.133, -1, 1, 9.333, -1, 9.533, 1, 9.733, 1, 1, 9.956, 1, 10.178, -1, 10.4, -1]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.089, 0, 0.178, 5.497, 0.267, 5.497, 1, 0.389, 5.497, 0.511, 0, 0.633, 0, 1, 0.733, 0, 0.833, 5.497, 0.933, 5.497, 1, 1.056, 5.497, 1.178, 0, 1.3, 0, 1, 1.4, 0, 1.5, 5.497, 1.6, 5.497, 1, 1.711, 5.497, 1.822, 0, 1.933, 0, 1, 2.044, 0, 2.156, 5.497, 2.267, 5.497, 1, 2.378, 5.497, 2.489, 0, 2.6, 0, 1, 2.7, 0, 2.8, 5.497, 2.9, 5.497, 1, 3.022, 5.497, 3.144, 0, 3.267, 0, 1, 3.356, 0, 3.444, 5.497, 3.533, 5.497, 1, 3.656, 5.497, 3.778, 0, 3.9, 0, 1, 4, 0, 4.1, 5.497, 4.2, 5.497, 1, 4.311, 5.497, 4.422, 0, 4.533, 0, 1, 4.644, 0, 4.756, 5.497, 4.867, 5.497, 1, 4.978, 5.497, 5.089, 0, 5.2, 0, 1, 5.3, 0, 5.4, 5.497, 5.5, 5.497, 1, 5.622, 5.497, 5.744, 0, 5.867, 0, 1, 5.956, 0, 6.044, 5.497, 6.133, 5.497, 1, 6.256, 5.497, 6.378, 0, 6.5, 0, 1, 6.6, 0, 6.7, 5.497, 6.8, 5.497, 1, 6.911, 5.497, 7.022, 0, 7.133, 0, 1, 7.244, 0, 7.356, 5.497, 7.467, 5.497, 1, 7.578, 5.497, 7.689, 0, 7.8, 0, 1, 7.9, 0, 8, 5.497, 8.1, 5.497, 1, 8.222, 5.497, 8.344, 0, 8.467, 0, 1, 8.567, 0, 8.667, 5.497, 8.767, 5.497, 1, 8.889, 5.497, 9.011, 0, 9.133, 0, 1, 9.222, 0, 9.311, 5.497, 9.4, 5.497, 1, 9.511, 5.497, 9.622, 0, 9.733, 0, 1, 9.844, 0, 9.956, 5.497, 10.067, 5.497, 1, 10.178, 5.497, 10.289, 0, 10.4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, -6, 1, 0.211, -6, 0.422, 6, 0.633, 6, 1, 0.856, 6, 1.078, -6, 1.3, -6, 1, 1.511, -6, 1.722, 6, 1.933, 6, 1, 2.156, 6, 2.378, -6, 2.6, -6, 1, 2.822, -6, 3.044, 6, 3.267, 6, 1, 3.478, 6, 3.689, -6, 3.9, -6, 1, 4.111, -6, 4.322, 6, 4.533, 6, 1, 4.756, 6, 4.978, -6, 5.2, -6, 1, 5.422, -6, 5.644, 6, 5.867, 6, 1, 6.078, 6, 6.289, -6, 6.5, -6, 1, 6.711, -6, 6.922, 6, 7.133, 6, 1, 7.356, 6, 7.578, -6, 7.8, -6, 1, 8.022, -6, 8.244, 6, 8.467, 6, 1, 8.689, 6, 8.911, -6, 9.133, -6, 1, 9.333, -6, 9.533, 6, 9.733, 6, 1, 9.956, 6, 10.178, -6, 10.4, -6]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.211, 0, 0.422, 0, 0.633, 0, 1, 0.856, 0, 1.078, 0, 1.3, 0, 1, 1.511, 0, 1.722, 0, 1.933, 0, 1, 2.156, 0, 2.378, 0, 2.6, 0, 1, 2.822, 0, 3.044, 0, 3.267, 0, 1, 3.478, 0, 3.689, 0, 3.9, 0, 1, 4.111, 0, 4.322, 0, 4.533, 0, 1, 4.756, 0, 4.978, 0, 5.2, 0, 1, 5.422, 0, 5.644, 0, 5.867, 0, 1, 6.078, 0, 6.289, 0, 6.5, 0, 1, 6.711, 0, 6.922, 0, 7.133, 0, 1, 7.356, 0, 7.578, 0, 7.8, 0, 1, 8.022, 0, 8.244, 0, 8.467, 0, 1, 8.689, 0, 8.911, 0, 9.133, 0, 1, 9.333, 0, 9.533, 0, 9.733, 0, 1, 9.956, 0, 10.178, 0, 10.4, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.211, 0, 0.422, 0, 0.633, 0, 1, 0.856, 0, 1.078, 0, 1.3, 0, 1, 1.511, 0, 1.722, 0, 1.933, 0, 1, 2.156, 0, 2.378, 0, 2.6, 0, 1, 2.822, 0, 3.044, 0, 3.267, 0, 1, 3.478, 0, 3.689, 0, 3.9, 0, 1, 4.111, 0, 4.322, 0, 4.533, 0, 1, 4.756, 0, 4.978, 0, 5.2, 0, 1, 5.422, 0, 5.644, 0, 5.867, 0, 1, 6.078, 0, 6.289, 0, 6.5, 0, 1, 6.711, 0, 6.922, 0, 7.133, 0, 1, 7.356, 0, 7.578, 0, 7.8, 0, 1, 8.022, 0, 8.244, 0, 8.467, 0, 1, 8.689, 0, 8.911, 0, 9.133, 0, 1, 9.333, 0, 9.533, 0, 9.733, 0, 1, 9.956, 0, 10.178, 0, 10.4, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 1, 0.211, 0, 0.422, 0, 0.633, 0, 1, 0.856, 0, 1.078, 0, 1.3, 0, 1, 1.511, 0, 1.722, 0, 1.933, 0, 1, 2.156, 0, 2.378, 0, 2.6, 0, 1, 2.822, 0, 3.044, 0, 3.267, 0, 1, 3.478, 0, 3.689, 0, 3.9, 0, 1, 4.111, 0, 4.322, 0, 4.533, 0, 1, 4.756, 0, 4.978, 0, 5.2, 0, 1, 5.422, 0, 5.644, 0, 5.867, 0, 1, 6.078, 0, 6.289, 0, 6.5, 0, 1, 6.711, 0, 6.922, 0, 7.133, 0, 1, 7.356, 0, 7.578, 0, 7.8, 0, 1, 8.022, 0, 8.244, 0, 8.467, 0, 1, 8.689, 0, 8.911, 0, 9.133, 0, 1, 9.333, 0, 9.533, 0, 9.733, 0, 1, 9.956, 0, 10.178, 0, 10.4, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.211, 0, 0.422, 0, 0.633, 0, 1, 0.856, 0, 1.078, 0, 1.3, 0, 1, 1.511, 0, 1.722, 0, 1.933, 0, 1, 2.156, 0, 2.378, 0, 2.6, 0, 1, 2.822, 0, 3.044, 0, 3.267, 0, 1, 3.478, 0, 3.689, 0, 3.9, 0, 1, 4.111, 0, 4.322, 0, 4.533, 0, 1, 4.756, 0, 4.978, 0, 5.2, 0, 1, 5.422, 0, 5.644, 0, 5.867, 0, 1, 6.078, 0, 6.289, 0, 6.5, 0, 1, 6.711, 0, 6.922, 0, 7.133, 0, 1, 7.356, 0, 7.578, 0, 7.8, 0, 1, 8.022, 0, 8.244, 0, 8.467, 0, 1, 8.689, 0, 8.911, 0, 9.133, 0, 1, 9.333, 0, 9.533, 0, 9.733, 0, 1, 9.956, 0, 10.178, 0, 10.4, 0]}, {"Target": "PartOpacity", "Id": "PartHead", "Segments": [0, 1, 0, 10.4, 1]}, {"Target": "PartOpacity", "Id": "PartHairFront", "Segments": [0, 1, 0, 10.4, 1]}, {"Target": "PartOpacity", "Id": "PartEyeL", "Segments": [0, 1, 0, 10.4, 1]}, {"Target": "PartOpacity", "Id": "PartEyeBallL", "Segments": [0, 1, 0, 10.4, 1]}, {"Target": "PartOpacity", "Id": "PartEyeWhiteL", "Segments": [0, 1, 0, 10.4, 1]}, {"Target": "PartOpacity", "Id": "PartEyeR", "Segments": [0, 1, 0, 10.4, 1]}, {"Target": "PartOpacity", "Id": "PartEyeBallR", "Segments": [0, 1, 0, 10.4, 1]}, {"Target": "PartOpacity", "Id": "PartEyeWhiteR", "Segments": [0, 1, 0, 10.4, 1]}, {"Target": "PartOpacity", "Id": "PartHairSide", "Segments": [0, 1, 0, 10.4, 1]}, {"Target": "PartOpacity", "Id": "PartMouth", "Segments": [0, 1, 0, 10.4, 1]}, {"Target": "PartOpacity", "Id": "PartOral", "Segments": [0, 1, 0, 10.4, 1]}, {"Target": "PartOpacity", "Id": "PartFace", "Segments": [0, 1, 0, 10.4, 1]}, {"Target": "PartOpacity", "Id": "PartBody", "Segments": [0, 1, 0, 10.4, 1]}, {"Target": "PartOpacity", "Id": "PartArmL", "Segments": [0, 1, 0, 10.4, 1]}, {"Target": "PartOpacity", "Id": "PartArmR", "Segments": [0, 1, 0, 10.4, 1]}, {"Target": "PartOpacity", "Id": "PartHairBack", "Segments": [0, 1, 0, 10.4, 1]}]}