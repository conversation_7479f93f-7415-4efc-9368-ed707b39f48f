{"Version": 3, "Meta": {"Duration": 1.6, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 22, "TotalSegmentCount": 110, "TotalPointCount": 308, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.333, 0, 0.467, -4, 0.6, -4, 0, 1.6, -4]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.289, 0, 0.378, -20, 0.467, -20, 1, 0.522, -20, 0.578, 2, 0.633, 2, 1, 0.667, 2, 0.7, 0, 0.733, 0, 1, 0.8, 0, 0.867, 0, 0.933, 0, 1, 1, 0, 1.067, -21, 1.133, -21, 1, 1.167, -21, 1.2, -17, 1.233, -17, 0, 1.6, -17]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.356, 0, 0.511, 0, 0.667, 0, 1, 0.778, 0, 0.889, 1, 1, 1, 0, 1.6, 1]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.067, 1, 0.133, 1, 0.2, 1, 1, 0.244, 1, 0.289, 1, 0.333, 1, 1, 0.378, 1, 0.422, 0, 0.467, 0, 1, 0.489, 0, 0.511, 0, 0.533, 0, 1, 0.578, 0, 0.622, 1, 0.667, 1, 1, 0.8, 1, 0.933, 1, 1.067, 1, 1, 1.122, 1, 1.178, 0.825, 1.233, 0.825, 0, 1.6, 0.825]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.544, 0, 0.889, 0, 1.233, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.067, 1, 0.133, 1, 0.2, 1, 1, 0.244, 1, 0.289, 1, 0.333, 1, 1, 0.378, 1, 0.422, 0, 0.467, 0, 1, 0.489, 0, 0.511, 0, 0.533, 0, 1, 0.578, 0, 0.622, 0.996, 0.667, 1, 1, 0.8, 1.011, 0.933, 1.013, 1.067, 1.013, 1, 1.122, 1.013, 1.178, 0.8, 1.233, 0.8, 0, 1.6, 0.8]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.322, 0, 0.444, 0, 0.567, 0, 1, 0.789, 0, 1.011, 0, 1.233, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.322, 0, 0.444, 0, 0.567, 0, 1, 0.789, 0, 1.011, 0, 1.233, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.322, 0, 0.444, 0, 0.567, 0, 1, 0.789, 0, 1.011, 0, 1.233, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.322, 0, 0.444, -1, 0.567, -1, 1, 0.789, -1, 1.011, -1, 1.233, -1, 0, 1.6, -1]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.322, 0, 0.444, -1, 0.567, -1, 1, 0.789, -1, 1.011, -1, 1.233, -1, 0, 1.6, -1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.067, 1, 0.133, 1, 0.2, 1, 1, 0.322, 1, 0.444, -2, 0.567, -2, 1, 0.789, -2, 1.011, -2, 1.233, -2, 0, 1.6, -2]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.322, 0, 0.444, 1, 0.567, 1, 1, 0.656, 1, 0.744, 1, 0.833, 1, 1, 0.889, 1, 0.944, 1, 1, 1, 1, 1.044, 1, 1.089, 0, 1.133, 0, 1, 1.244, 0, 1.356, 0, 1.467, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.156, 0, 0.311, 0, 0.467, 0, 1, 0.556, 0, 0.644, -10, 0.733, -10, 1, 0.833, -10, 0.933, -9, 1.033, -9, 0, 1.6, -9]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.278, 0, 0.356, -4, 0.433, -4, 1, 0.522, -4, 0.611, 5, 0.7, 5, 1, 0.789, 5, 0.878, 5, 0.967, 5, 1, 1.044, 5, 1.122, -7, 1.2, -7, 1, 1.267, -7, 1.333, 0, 1.4, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 0, 1.6, 0]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.233, 0, 0.267, 0, 0.3, 0, 1, 0.367, 0, 0.433, -4.5, 0.5, -4.5, 1, 0.578, -4.5, 0.656, 0.8, 0.733, 0.8, 1, 0.811, 0.8, 0.889, -0.156, 0.967, -0.6, 1, 1.011, -0.854, 1.056, -0.72, 1.1, -1, 1, 1.167, -1.42, 1.233, -3.7, 1.3, -3.7, 1, 1.356, -3.7, 1.411, -2.4, 1.467, -2.4, 0, 1.6, -2.4]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.233, 0, 0.267, 0, 0.3, 0, 1, 0.367, 0, 0.433, -4.5, 0.5, -4.5, 1, 0.578, -4.5, 0.656, 2.2, 0.733, 2.2, 1, 0.811, 2.2, 0.889, -0.749, 0.967, -0.9, 1, 1.011, -0.987, 1.056, -0.921, 1.1, -1, 1, 1.167, -1.119, 1.233, -3.6, 1.3, -3.6, 1, 1.356, -3.6, 1.411, -1.8, 1.467, -1.8, 0, 1.6, -1.8]}, {"Target": "Parameter", "Id": "ParamHairAhoge", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0, 0.2, 0, 1, 0.289, 0, 0.378, 6, 0.467, 6, 1, 0.5, 6, 0.533, 4, 0.567, 0, 1, 0.622, -6.667, 0.678, -10, 0.733, -10, 1, 0.767, -10, 0.8, -6, 0.833, -6, 1, 0.933, -6, 1.033, -10, 1.133, -10, 1, 1.178, -10, 1.222, -2, 1.267, -2, 1, 1.311, -2, 1.356, -9, 1.4, -9, 0, 1.6, -9]}, {"Target": "PartOpacity", "Id": "PartArmA", "Segments": [0, 1, 0, 1.6, 1]}]}