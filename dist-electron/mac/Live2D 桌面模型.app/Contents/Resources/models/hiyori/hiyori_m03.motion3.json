{"Version": 3, "Meta": {"Duration": 4.2, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": false, "CurveCount": 22, "TotalSegmentCount": 133, "TotalPointCount": 377, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.389, 0, 0.611, 0, 0.833, 0, 1, 0.933, 0, 1.033, 0, 1.133, 0, 1, 1.311, 0, 1.489, 0, 1.667, 0, 1, 1.778, 0, 1.889, -0.268, 2, -1, 1, 2.089, -1.585, 2.178, -2, 2.267, -2, 0, 4.2, -2]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.3, 0, 0.433, -18, 0.567, -18, 1, 0.656, -18, 0.744, 8, 0.833, 8, 1, 0.933, 8, 1.033, -8, 1.133, -8, 1, 1.311, -8, 1.489, 10, 1.667, 10, 1, 1.778, 10, 1.889, -21, 2, -21, 1, 2.089, -21, 2.178, -3, 2.267, -3, 0, 4.2, -3]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.289, 0, 0.411, -1, 0.533, -1, 1, 0.722, -1, 0.911, 17, 1.1, 17, 1, 1.411, 17, 1.722, -22, 2.033, -22, 1, 2.244, -22, 2.456, 11, 2.667, 11, 0, 4.2, 11]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 0, 4.2, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.2, 1, 0.233, 1, 0.267, 1, 1, 0.322, 1, 0.378, 0, 0.433, 0, 1, 0.478, 0, 0.522, 0, 0.567, 0, 1, 0.644, 0, 0.722, 1, 0.8, 1, 1, 1.056, 1, 1.311, 0.988, 1.567, 0.988, 1, 1.789, 0.988, 2.011, 1, 2.233, 1, 1, 2.267, 1, 2.3, 1, 2.333, 1, 1, 2.367, 1, 2.4, 0, 2.433, 0, 1, 2.478, 0, 2.522, 0, 2.567, 0, 1, 2.622, 0, 2.678, 1, 2.733, 1, 0, 4.2, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 0, 4.2, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.2, 1, 0.233, 1, 0.267, 1, 1, 0.322, 1, 0.378, 0, 0.433, 0, 1, 0.478, 0, 0.522, 0, 0.567, 0, 1, 0.644, 0, 0.722, 1, 0.8, 1, 1, 1.056, 1, 1.311, 1, 1.567, 1, 1, 1.789, 1, 2.011, 1, 2.233, 1, 1, 2.267, 1, 2.3, 1, 2.333, 1, 1, 2.367, 1, 2.4, 0, 2.433, 0, 1, 2.478, 0, 2.522, 0, 2.567, 0, 1, 2.622, 0, 2.678, 1, 2.733, 1, 0, 4.2, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 0, 4.2, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.256, 0, 0.344, 0, 0.433, 0, 1, 0.478, 0, 0.522, -1, 0.567, -1, 1, 0.8, -1, 1.033, -1, 1.267, -1, 1, 1.422, -1, 1.578, 0.583, 1.733, 0.583, 1, 1.967, 0.583, 2.2, 0.583, 2.433, 0.583, 1, 2.478, 0.583, 2.522, 0, 2.567, 0, 0, 4.2, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.256, 0, 0.344, 0, 0.433, 0, 1, 0.478, 0, 0.522, 1, 0.567, 1, 1, 0.8, 1, 1.033, 1, 1.267, 1, 1, 1.422, 1, 1.578, 0.75, 1.733, 0.75, 1, 1.967, 0.75, 2.2, 0.75, 2.433, 0.75, 1, 2.478, 0.75, 2.522, 0, 2.567, 0, 0, 4.2, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 0, 4.2, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 0, 4.2, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 0, 4.2, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 0, 4.2, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 0, 4.2, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, -4, 0.4, -4, 1, 0.478, -4, 0.556, 10, 0.633, 10, 1, 0.8, 10, 0.967, -0.386, 1.133, -0.386, 1, 1.244, -0.386, 1.356, 10, 1.467, 10, 1, 1.6, 10, 1.733, 0, 1.867, 0, 1, 2.044, 0, 2.222, 10, 2.4, 10, 1, 2.444, 10, 2.489, 10.395, 2.533, 9.649, 1, 2.7, 6.854, 2.867, -3.526, 3.033, -3.526, 1, 3.267, -3.526, 3.5, 6.807, 3.733, 6.807, 1, 3.811, 6.807, 3.889, 6, 3.967, 6, 0, 4.2, 6]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.256, 0, 0.344, -1, 0.433, -1, 1, 0.656, -1, 0.878, 6, 1.1, 6, 1, 1.333, 6, 1.567, -3, 1.8, -3, 1, 2.256, -3, 2.711, 8, 3.167, 8, 0, 4.2, 8]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.189, 0, 0.378, 1, 0.567, 1, 1, 0.733, 1, 0.9, 0, 1.067, 0, 1, 1.3, 0, 1.533, 1, 1.767, 1, 1, 2.033, 1, 2.3, 0, 2.567, 0, 1, 2.778, 0, 2.989, 1, 3.2, 1, 1, 3.4, 1, 3.6, 0, 3.8, 0, 0, 4.2, 0]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, -10, 1, 0.256, -10, 0.511, 0, 0.767, 0, 1, 0.889, 0, 1.011, -6.5, 1.133, -6.5, 1, 1.289, -6.5, 1.444, 0, 1.6, 0, 1, 1.756, 0, 1.911, -8, 2.067, -8, 1, 2.344, -8, 2.622, -1.4, 2.9, -1.4, 1, 2.978, -1.4, 3.056, -1.775, 3.133, -2.3, 1, 3.211, -2.825, 3.289, -3, 3.367, -3, 1, 3.433, -3, 3.5, -2.5, 3.567, -2.5, 1, 3.733, -2.5, 3.9, -3, 4.067, -3, 0, 4.2, -3]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, -10, 1, 0.256, -10, 0.511, 0, 0.767, 0, 1, 0.867, 0, 0.967, -5.2, 1.067, -5.2, 1, 1.244, -5.2, 1.422, 0, 1.6, 0, 1, 1.778, 0, 1.956, -6.3, 2.133, -6.3, 1, 2.422, -6.3, 2.711, 0.029, 3, 0.029, 1, 3.144, 0.029, 3.289, 0, 3.433, 0, 1, 3.5, 0, 3.567, 0, 3.633, 0, 1, 3.722, 0, 3.811, -0.029, 3.9, -0.029, 0, 4.2, -0.029]}, {"Target": "Parameter", "Id": "ParamHairAhoge", "Segments": [0, 0, 1, 0.122, 0, 0.244, -3.127, 0.367, -4, 1, 0.511, -5.032, 0.656, -5, 0.8, -5, 1, 0.9, -5, 1, 10, 1.1, 10, 1, 1.267, 10, 1.433, 10.072, 1.6, 8, 1, 1.722, 6.481, 1.844, -10, 1.967, -10, 1, 2.122, -10, 2.278, -10.146, 2.433, -9.434, 1, 2.589, -8.722, 2.744, 10, 2.9, 10, 1, 3.011, 10, 3.122, -5.646, 3.233, -5.646, 1, 3.367, -5.646, 3.5, 3.825, 3.633, 3.825, 1, 3.767, 3.825, 3.9, 0, 4.033, 0, 0, 4.2, 0]}, {"Target": "PartOpacity", "Id": "PartArmA", "Segments": [0, 1, 0, 4.2, 1]}]}