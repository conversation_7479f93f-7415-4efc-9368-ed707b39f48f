{"Version": 3, "Meta": {"PhysicsSettingCount": 6, "TotalInputCount": 18, "TotalOutputCount": 7, "VertexCount": 12, "Fps": 60, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "髪揺れ　前"}, {"Id": "PhysicsSetting2", "Name": "髪揺れ　横"}, {"Id": "PhysicsSetting3", "Name": "髪揺れ　後"}, {"Id": "PhysicsSetting4", "Name": "髪ふわ　前"}, {"Id": "PhysicsSetting5", "Name": "髪ふわ　横"}, {"Id": "PhysicsSetting6", "Name": "髪ふわ　後"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairFront"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 11.4}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 1.2, "Radius": 11.4}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairSide"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairSide2"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 1.3, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBack"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairFrontFuwa"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 1.2, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairSideFuwa"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9.7}, "Mobility": 0.92, "Delay": 0.8, "Acceleration": 1.3, "Radius": 9.7}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBackFuwa"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 11.4}, "Mobility": 0.88, "Delay": 0.69, "Acceleration": 1.36, "Radius": 11.4}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}]}