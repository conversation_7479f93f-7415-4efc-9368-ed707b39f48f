directories:
  output: dist-electron
  buildResources: build
appId: com.live2d.desktop-pet
productName: Live2D 桌面模型
files:
  - filter:
      - dist/**/*
      - electron/**/*
      - public/models/**/*
      - public/library/**/*
      - public/audio/**/*
      - node_modules/**/*
extraResources:
  - from: public/models
    to: models
  - from: public/library
    to: library
  - from: public/audio
    to: audio
mac:
  category: public.app-category.entertainment
  icon: public/icon.png
  target:
    - target: dmg
      arch:
        - x64
        - arm64
    - target: zip
      arch:
        - x64
        - arm64
win:
  icon: public/icon.png
  target:
    - target: nsis
      arch:
        - x64
        - ia32
    - target: portable
      arch:
        - x64
        - ia32
linux:
  icon: public/icon.png
  category: Game
  target:
    - target: AppImage
      arch:
        - x64
    - target: deb
      arch:
        - x64
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: Live2D 桌面模型
electronVersion: 37.2.5
